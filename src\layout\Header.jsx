import React from 'react';

const Header = () => {
  const headerStyle = {
    background: 'linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%)',
    padding: '1rem 0',
    position: 'sticky',
    top: 0,
    zIndex: 1000,
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)'
  };

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '0 2rem'
  };

  const logoStyle = {
    fontSize: '1.8rem',
    fontWeight: 'bold',
    color: '#2c3e50',
    margin: 0,
    lineHeight: '1.2',
    letterSpacing: '1px'
  };

  const navStyle = {
    display: 'flex',
    listStyle: 'none',
    margin: 0,
    padding: 0,
    gap: '2rem'
  };

  const linkStyle = {
    textDecoration: 'none',
    color: '#2c3e50',
    fontWeight: '500',
    fontSize: '0.95rem',
    letterSpacing: '0.5px',
    transition: 'color 0.3s ease'
  };

  const activeLinkStyle = {
    ...linkStyle,
    color: '#27ae60',
    borderBottom: '2px solid #27ae60',
    paddingBottom: '5px'
  };

  const iconsStyle = {
    display: 'flex',
    gap: '1rem',
    alignItems: 'center'
  };

  const iconBtnStyle = {
    background: 'none',
    border: 'none',
    color: '#2c3e50',
    cursor: 'pointer',
    padding: '0.5rem',
    borderRadius: '50%',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };

  return (
    <header style={headerStyle}>
      <div style={containerStyle}>
        <div>
          <h1 style={logoStyle}>HA<br />SPORT</h1>
        </div>

        <nav>
          <ul style={navStyle}>
            <li><a href="#home" style={activeLinkStyle}>HOME</a></li>
            <li><a href="#catalogs" style={linkStyle}>CATALOGS</a></li>
            <li><a href="#blog" style={linkStyle}>BLOG</a></li>
            <li><a href="#shop" style={linkStyle}>SHOP</a></li>
            <li><a href="#contact" style={linkStyle}>CONTACT US</a></li>
          </ul>
        </nav>

        <div style={iconsStyle}>
          <button style={iconBtnStyle}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
          <button style={iconBtnStyle}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </button>
          <button style={iconBtnStyle}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"></path>
              <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"></path>
              <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
